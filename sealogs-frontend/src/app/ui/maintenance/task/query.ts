import gql from 'graphql-tag'

// CREW_LIST
export const ReadSeaLogsMembers = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: AS<PERSON>, surname: <PERSON><PERSON> }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                firstName
                surname
            }
        }
    }
`
