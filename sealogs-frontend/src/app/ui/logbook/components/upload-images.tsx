'use client'

import React, { use, useEffect, useRef, useState } from 'react'
import AWS from 'aws-sdk'
import { AlertDialogNew, Button } from '@/components/ui'
import { toast } from '@/hooks/use-toast'
import { Camera } from 'lucide-react'
import { cn } from '../../../../../utils/cn'
import { useSearchParams } from 'next/navigation'
import { useMutation } from '@apollo/client'
import { CREATE_SECTION_MEMBER_IMAGE } from '@/app/lib/graphQL/mutation'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

export default function UploadCloudFlareCaptures({
    files,
    setFiles,
    inputId,
}: {
    files: any
    setFiles: any
    inputId?: string
}) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const [openCameraDialog, setOpenCameraDialog] = useState(false)
    const [image, setImage] = useState<any>(false)
    const [displayImage, setDisplayImage] = useState(false)
    const [clientID, setClientID] = useState(0)

    // Handle opening the camera dialog
    const handleOpenCameraDialog = () => {
        setOpenCameraDialog(true)
        navigator.mediaDevices
            .getUserMedia({ video: true })
            .then((stream) => {
                const videoElement = document.getElementById(
                    'camera-video',
                ) as HTMLVideoElement
                videoElement.srcObject = stream
                videoElement.play()
            })
            .catch((error) => {
                console.error('Error accessing camera:', error)
            })
    }

    const captureImage = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (!videoElement) {
            console.error('Video element not found')
            return
        }
        const canvas = document.createElement('canvas')
        canvas.width = videoElement.videoWidth
        canvas.height = videoElement.videoHeight
        const context = canvas.getContext('2d')
        if (!context) {
            console.error('Failed to get canvas context')
            return
        }
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
        const imageData = canvas.toDataURL('image/png')

        // Stop the camera stream after capturing the image
        if (videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
        if (imageData) {
            setImage(imageData)
            setDisplayImage(true)
        }
    }

    const turnOffCamera = () => {
        const videoElement = document.getElementById(
            'camera-video',
        ) as HTMLVideoElement
        if (videoElement && videoElement.srcObject) {
            const stream = videoElement.srcObject as MediaStream
            const tracks = stream.getTracks()
            tracks.forEach((track) => track.stop())
            videoElement.srcObject = null
        }
    }

    useEffect(() => {
        setClientID(+(localStorage.getItem('clientId') ?? 0))
    }, [])

    useEffect(() => {
        if (openCameraDialog) return
        turnOffCamera()
    }, [openCameraDialog])

    const [createSectionMemberImage] = useMutation(
        CREATE_SECTION_MEMBER_IMAGE,
        {
            onCompleted: () => {
                setFiles()
            },
            onError: (error) => {
                console.error('Error updating comment', error)
            },
        },
    )

    async function uploadFile(file: any) {
        // Upload file to Cloudflare
        var fileName = file?.name || Date.now()
        fileName = clientID + '-capture-' + fileName
        createSectionMemberImage({
            variables: {
                input: {
                    name: fileName,
                    fieldName: inputId,
                    imageType: 'FieldImage',
                    logBookEntryID: logentryID,
                },
            },
        })
        s3Client.putObject(
            {
                Bucket: 'captures',
                Key: clientID + '-capture-' + fileName,
                Body: file,
            },
            (err, data) => {
                if (err) {
                    console.error(err)
                } else {
                    setFiles([{ title: clientID + '-' + fileName }])
                }
            },
        )
    }

    const getFile = (file: any) => () => {
        s3Client.getObject(
            {
                Bucket: 'captures',
                Key: file,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                } else {
                    const fileType = file.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)
                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        setImage(url)
                        setDisplayImage(true)
                    } else if (fileType.match(/^(pdf)$/i)) {
                        const pdfBlob = new Blob([data?.Body as Uint8Array], {
                            type: 'application/pdf',
                        })
                        const pdfUrl = URL.createObjectURL(pdfBlob)
                        window.open(pdfUrl, '_blank')
                        URL.revokeObjectURL(pdfUrl)
                    } else {
                        toast({
                            description:
                                'File type not supported to view. Please save the file to view.',
                            variant: 'destructive',
                        })
                        const link = document.createElement('a')
                        link.target = '_blank'
                        link.href = url
                        link.download = file
                        link.click()
                        URL.revokeObjectURL(url)
                    }
                }
            },
        )
    }

    return (
        <>
            <Button
                variant="ghost"
                size="icon"
                iconOnly
                title="Add comment"
                className="group"
                iconLeft={
                    <Camera
                        className={cn(
                            'text-neutral-400 group-hover:text-neutral-400/50',
                            'will-change-transform will-change-width will-change-padding transform-gpu',
                            'group-hover:transition-colors group-hover:ease-out group-hover:duration-300',
                        )}
                        size={24}
                    />
                }
                onClick={handleOpenCameraDialog}
            />
            <AlertDialogNew
                openDialog={openCameraDialog}
                setOpenDialog={setOpenCameraDialog}
                title="Camera"
                handleCreate={() => {
                    if (image) {
                        uploadFile(image)
                        setOpenCameraDialog(false)
                    } else {
                        toast({
                            description: 'Please capture an image first.',
                            variant: 'destructive',
                        })
                    }
                }}
                handleCancel={() => {
                    setOpenCameraDialog(false)
                    setImage(null)
                    setDisplayImage(false)
                    turnOffCamera()
                }}
                actionText="Upload"
                cancelText="Close"
                loading={false}>
                <div className="flex flex-col items-center">
                    <video
                        id="camera-video"
                        style={{
                            display: displayImage ? 'none' : 'block',
                        }}></video>
                    <img
                        src={image}
                        alt="Captured"
                        style={{ display: displayImage ? 'block' : 'none' }}
                    />
                </div>
                <div className="flex flex-col items-center mt-4">
                    {!displayImage && (
                        <Button onClick={captureImage}>Capture</Button>
                    )}
                    {displayImage && (
                        <Button
                            onClick={() => {
                                setImage(null)
                                setDisplayImage(false)
                                handleOpenCameraDialog()
                            }}
                            className="mt-2">
                            Recapture
                        </Button>
                    )}
                </div>
            </AlertDialogNew>
        </>
    )
}
