import gql from 'graphql-tag'

export const GET_SECTION_MEMBER_IMAGES = gql`
    query GetCaptureImages(
        $limit: Int = 500
        $offset: Int = 0
        $filter: CaptureImageFilterFields = {}
    ) {
        readCaptureImages(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                imageType
                fieldName
                name
                logBookEntrySectionID
                logBookEntryID
            }
        }
    }
`
